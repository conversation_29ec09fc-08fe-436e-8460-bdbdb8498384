"use client";

import type React from "react";

import { createContext, useContext, useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { apiLogin } from "@/api/authAPI"
import { useUserStore } from "./useUserStore"
import { User } from "@/api/models/user"


type Role =
  | "guest"
  | "user"
  | "talent"
  | "business"
  | "mod"
  | "admin"
  | "superadmin";

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (data: any) => void;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      const response = await apiLogin({ email, password });

      setUser(response.user);
      // Store user and token in localStorage
      localStorage.setItem("user", JSON.stringify(response.user));
      localStorage.setItem("token", response.token);

      // add to zustand store
      useUserStore.getState().setUser(response.user);
      useUserStore.getState().setToken(response.token);

      // set to cookies 
      setCookie("user", JSON.stringify(response.user), {
        maxAge: 60 * 60 * 24 * 7, // 1 week
        path: "/",
      });

      if (response.user.roles?.some((role) => role.name === "superadmin")) {
        router.push("/admin/dashboard");
      } else if (response.user.roles?.some((role) => role.name === "admin")) {
        router.push("/admin/dashboard");
      } else if (response.user.roles?.some((role) => role.name === "mod")) {
        router.push("/admin/dashboard");
      } else if (
        response.user.roles?.some((role) => role.name === "business")
      ) {
        router.push("/business/dashboard");
      } else if (response.user.roles?.some((role) => role.name === "talent")) {
        router.push("/talent/dashboard");
      } else {
        router.push("/user/dashboard")
      }
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem("user");
    router.push("/");
  };

  const updateUser = (response: any) => {
    console.log("response", response);

    const oldUser = useUserStore.getState().user;
    const updatedUser = { ...oldUser, ...response };

    setUser(response);
    localStorage.setItem("user", JSON.stringify(response));
    useUserStore.getState().setUser(updatedUser);
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, loading, updateUser }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

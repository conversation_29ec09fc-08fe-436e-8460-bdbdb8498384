import { RecentTransactions } from "@/components/recent-transactions"
import { DesignaliCreative } from "@/components/creative"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>R<PERSON>, ChevronLeft, ChevronRight, Clock, Search, Star } from "lucide-react"
import { CategoryCard } from "@/components/user-home/category-card"
import { OccasionCard } from "@/components/user-home/occasion-card"
import { TalentCard } from "@/components/user-home/talent-card"
import { InstantVideoCard } from "@/components/user-home/instant-video-card"
import { HowItWorksCard } from "@/components/user-home/how-it-works-card"
import { ReviewCard } from "@/components/user-home/review-card"
import CategoriesSeaction from "@/components/home/<USER>"
import TalentSeaction from "@/components/home/<USER>"
import InstantVideoSection from "@/components/home/<USER>"

export default function Dashboard() {
  return (
    <>
      <main className="min-h-screen container mx-auto px-4">
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="font-bold mb-6 text-center text-2xl">Personalized videos from your favorite stars</h2>
            {/* <CategoriesSeaction /> */}
          </div>
        </section>
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-xl font-bold mb-6">Personalized videos for every occasion</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <OccasionCard
                title="Birthdays"
                description="Personalized videos for"
                imageUrl="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=308,height=175/https://cdn.cameo.com/resizer/7725d15c-7503-4d8d-88a6-4f9e37f0df58.jpg"
              />
              <OccasionCard
                title="Graduation"
                description="Videos to celebrate"
                imageUrl="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=308,height=175/https://cdn.cameo.com/resizer/c38522a2-b847-47da-8db1-4a41ccc43998.jpg"
              />
              <OccasionCard
                title="Weddings"
                description="The perfect gift for"
                imageUrl="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=308,height=175/https://cdn.cameo.com/resizer/c4aabf7c-66a6-4307-8eda-26a9bbf79dc5.jpg"
              />
              <OccasionCard
                title="Father's Day"
                description="Get him the best gift for"
                imageUrl="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=308,height=175/https://cdn.cameo.com/resizer/06e43921-0975-41f5-8e9e-dd91538a7129.jpg"
              />
            </div>
          </div>
        </section>
        {/* <TalentSeaction /> */}

        {/* <InstantVideoSection /> */}

        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-xl font-bold mb-6">Gifts for every budget</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <div>
                <Image
                  src="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=450,height=250/https://cdn.cameo.com/resizer/494d89a9-2dd8-4c4b-8dc6-01f17477a5d6.jpg"
                  alt="Under $25"
                  width={450}
                  height={250}
                  className="rounded-xl"
                />
              </div>
              <div>
                <Image
                  src="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=450,height=250/https://cdn.cameo.com/resizer/c1927584-39e3-478c-9af7-b540ffba9b1f.jpg"
                  alt="Under $50"
                  width={450}
                  height={250}
                  className="rounded-xl"
                />
              </div>
              <div>
                <Image
                  src="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=450,height=250/https://cdn.cameo.com/resizer/3581b626-3812-476a-a548-4d2a33a2b59a.jpg"
                  alt="Under $100"
                  width={450}
                  height={250}
                  className="rounded-xl"
                />
              </div>
              <div>
                <Image
                  src="https://www.cameo.com/cdn-cgi/image/fit=cover,format=auto,width=450,height=250/https://cdn.cameo.com/resizer/3c36317f-67b8-4c8e-b479-136838043dd9.jpg"
                  alt="Under $150"
                  width={450}
                  height={250}
                  className="rounded-xl"
                />
              </div>
            </div>
          </div>
        </section>
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-xl font-bold mb-2">How Cameo works</h2>
            <p className="text-sm text-gray-400 mb-6">
              Get a personalized video in just four steps.{" "}
              <Link href="/learn-more" className="text-black hover:underline">
                Learn more
              </Link>
            </p>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <HowItWorksCard
                icon={<Search className="h-5 w-5" />}
                title="Find a celebrity"
                description="Browse thousands of stars offering personalized videos."
              />
              <HowItWorksCard
                icon={<ArrowRight className="h-5 w-5" />}
                title="Tell them what to say"
                description="During checkout, you'll provide the details for your personalized video."
              />
              <HowItWorksCard
                icon={<Star className="h-5 w-5" />}
                title="Get your video"
                description="Most videos deliver within 7 days to complete your request. When it's ready, we'll send a viewing link to your email."
              />
              <HowItWorksCard
                icon={<ChevronRight className="h-5 w-5" />}
                title="Share with loved ones"
                description="Your video will delight family and friends! Don't forget to capture their reactions and share on social media."
              />
            </div>
          </div>
        </section>
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-xl font-bold mb-6">Recent reviews</h2>
            <div className="relative">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <ReviewCard
                  name="Birthday from Joe Wilkins"
                  rating={5}
                  review="Thank you thank you sooooo! Ingram was beyond thrilled to get your message and couldn't stop laughing. You did an amazing job of making him!"
                />
                <ReviewCard
                  name="Advice from Alex Macqueen"
                  rating={5}
                  review="My son's best friend gets a pep talk from his hero before taking exams. A+ service from Alex. Brilliant actor and all-round nice guy!"
                />
                <ReviewCard
                  name="Advice from Max Sievers"
                  rating={5}
                  review="Max is a multiple times, several if not 5 times, and continues to be the best for making me laugh!"
                />
                <ReviewCard
                  name="Motivation from Ian Somehalder"
                  rating={5}
                  review="Thank you for the video you made for me! I'm still freaking you for clicking me! You are such a kind soul and thank you for spirits up so much!"
                />
              </div>
            </div>
          </div>
        </section>
        <section className="py-8">
            <h2 className="text-xl font-bold mb-6">Categories</h2>
            {/* <CategoriesSeaction /> */}
        </section>
        <section className="py-8">
          <div className="container mx-auto px-4">
            <h2 className="text-xl font-bold mb-6">This is Cameo</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h3 className="font-bold mb-2">Gifts as unique as the people you're gifting to</h3>
                <p className="text-sm text-gray-400">
                  Cameo connects fans with their favorite celebrities, creating one-of-a-kind connections between
                  celebrities and the people they inspire.
                </p>
              </div>
              <div>
                <h3 className="font-bold mb-2">Perfect for every occasion (or just because)</h3>
                <p className="text-sm text-gray-400">
                  From birthdays to pep talks to wedding wishes, Cameo helps you bring magic into everyday moments both
                  big and small.
                </p>
              </div>
              <div>
                <h3 className="font-bold mb-2">Someone for every fan</h3>
                <p className="text-sm text-gray-400">
                  Everyone has someone they're fans of. With thirty thousand celebrities, there's a star for every kind
                  of fan on Cameo.
                </p>
              </div>
            </div>
          </div>
        </section>
      </main>
    </>
  )
}

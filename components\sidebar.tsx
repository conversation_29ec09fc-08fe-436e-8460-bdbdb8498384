"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  Home,
  ShoppingBag,
  MessageSquare,
  User,
  Bell,
  LogOut,
  LayoutDashboard,
  Users,
  Settings,
  AlertTriangle,
  Video,
  Briefcase,
  DollarSign,
  HelpCircle,
  ShieldCheck,
  Star,
  Menu,
  ChevronLeft,
  Bookmark,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { <PERSON>ltip, TooltipContent, TooltipTrigger, TooltipProvider } from "@/components/ui/tooltip"
import { useAuth } from "@/hooks/use-auth"
import { useUserStore } from "@/hooks/useUserStore"
import { hr } from "date-fns/locale"


const bottomNavigation = [
  { name: "Settings", href: "/settings", icon: Settings },
  { name: "Help", href: "/help", icon: HelpCircle },
]

export function Sidebar() {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const user = useUserStore((state) => state.user);
  type Role = keyof typeof menuItems;
  const role : Role = user?.roles?.[0]?.name as Role || "guest";


  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`)
  }
  const menuItems = {
  guest: [
    { name: "Trang chủ", href: "/", icon: Home },
    { name: "Đăng nhập", href: "/dang-nhap", icon: User },
    { name: "Đăng ký người dùng", href: "/dang-ky", icon: User },
    { name: "Đăng ký đối tác", href: "/dang-ky-doi-tac", icon: User },
    // { name: "Câu hỏi thường gặp", href: "/cau-hoi-thuong-gap", icon: HelpCircle },
  ],
  user: [
    { name: "Trang chủ", href: "/trang-chu", icon: Home },
    { name: "Đơn hàng", href: "/don-hang", icon: ShoppingBag },
    { name: "Chat", href: "/chat", icon: MessageSquare },
    { name: "Tài khoản", href: "/tai-khoan", icon: User },
    { name: "Thông báo", href: "/thong-bao", icon: Bell },
  ],
  talent: [
    { name: "Dashboard", href: "/doi-tac", icon: LayoutDashboard },
    { name: "Đơn hàng", href: "/doi-tac/don-hang", icon: ShoppingBag },
    { name: "Chat", href: "/doi-tac/chat", icon: MessageSquare },
    { name: "Hồ sơ", href: "/doi-tac/ho-so", icon: User },
    { name: "Thanh toán", href: "/doi-tac/thanh-toan", icon: DollarSign },
    { name: "Sản phẩm", href: "/doi-tac/san-pham", icon: Video },
  ],
  business: [
    { name: "Dashboard", href: "/doi-tac", icon: LayoutDashboard },
    { name: "Sản phẩm", href: "/doi-tac/san-pham", icon: Video },
    { name: "Talent", href: "/doi-tac/talent", icon: Star },
    { name: "Đơn hàng", href: "/doi-tac/don-hang", icon: ShoppingBag },
    { name: "Chat", href: "/doi-tac/chat", icon: MessageSquare },
    { name: "Hồ sơ", href: "/doi-tac/ho-so", icon: Briefcase },
    { name: "Thanh toán", href: "/doi-tac/thanh-toan", icon: DollarSign },
  ],
  mod: [
    { name: "Dashboard", href: "/admin/dashboard", icon: LayoutDashboard },
    { name: "Partner", href: "/admin/partner", icon: Users },
    { name: "Đơn hàng", href: "/admin/don-hang", icon: ShoppingBag },
    { name: "Chat", href: "/admin/chat", icon: MessageSquare },
    { name: "Người dùng", href: "/admin/nguoi-dung", icon: User },
    { name: "Khiếu nại", href: "/admin/khieu-nai", icon: AlertTriangle },
  ],
  admin: [
    { name: "Dashboard", href: "/admin/dashboard", icon: LayoutDashboard },
    { name: "Đối tác", href: "/admin/doi-tac", icon: Users },
    { name: "Đơn hàng", href: "/admin/don-hang", icon: ShoppingBag },
    {name : "Danh mục", href: "/admin/danh-muc", icon: Bookmark},
    { name: "Chat", href: "/admin/chat", icon: MessageSquare },
    { name: "Người dùng", href: "/admin/nguoi-dung", icon: User },
    { name: "Khiếu nại", href: "/admin/khieu-nai", icon: AlertTriangle },
    // { name: "Cài đặt", href: "/admin/cai-dat", icon: Settings },
  ],
  superadmin: [
    { name: "Dashboard", href: "/admin/dashboard", icon: LayoutDashboard },
    { name: "Partner", href: "/admin/partner", icon: Users },
    { name: "Đơn hàng", href: "/admin/don-hang", icon: ShoppingBag },
    { name: "Chat", href: "/admin/chat", icon: MessageSquare },
    { name: "Người dùng", href: "/admin/nguoi-dung", icon: User },
    { name: "Khiếu nại", href: "/admin/khieu-nai", icon: AlertTriangle },
    // { name: "Cài đặt", href: "/admin/cai-dat", icon: Settings },
    { name: "Quản lý Admin", href: "/admin/quan-ly-admin", icon: ShieldCheck },
  ],
}

  const currentMenuItems = menuItems[role]|| menuItems.guest
  type NavItemType = {
    name: string
    href: string
    icon: React.ComponentType<{ className?: string }>
  }

  const NavItem = ({
    item,
    isBottom = false,
  }: {
    item: NavItemType
    isBottom?: boolean
  }) => (
    <Tooltip delayDuration={0}>
      <TooltipTrigger asChild>
        <Link
          href={item.href}
          className={cn(
            "flex items-center rounded-md px-3 py-2 text-sm font-medium transition-colors",
            pathname === item.href
              ? "bg-secondary text-secondary-foreground"
              : "text-muted-foreground hover:bg-secondary hover:text-secondary-foreground",
            isCollapsed && "justify-center px-2",
          )}
        >
          <item.icon className={cn("h-4 w-4", !isCollapsed && "mr-3")} />
          {!isCollapsed && <span>{item.name}</span>}
        </Link>
      </TooltipTrigger>
      {isCollapsed && (
        <TooltipContent side="right" className="flex items-center gap-4">
          {item.name}
        </TooltipContent>
      )}
    </Tooltip>
  )

  return (
    <TooltipProvider>
      <>
        <button
          className="lg:hidden fixed top-4 left-4 z-50 p-2 bg-background rounded-md shadow-md"
          onClick={() => setIsMobileOpen(!isMobileOpen)}
          aria-label="Toggle sidebar"
        >
          <Menu className="h-6 w-6" />
        </button>
        <div
          className={cn(
            "fixed inset-y-0 z-20 flex flex-col bg-background transition-all duration-300 ease-in-out lg:static",
            isCollapsed ? "w-[72px]" : "w-56",
            isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
          )}
        >
          <div className="border-b border-border">
            <div className={cn("flex h-16 items-center gap-2 px-4", isCollapsed && "justify-center px-2")}>
              {!isCollapsed && (
                <Link href="/" className="flex items-center font-semibold">
                  <span className="text-lg">Bùi Sơn</span>
                </Link>
              )}
              <Button
                variant="ghost"
                size="sm"
                className={cn("ml-auto h-8 w-8", isCollapsed && "ml-0")}
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                <ChevronLeft className={cn("h-4 w-4 transition-transform", isCollapsed && "rotate-180")} />
                <span className="sr-only">{isCollapsed ? "Expand" : "Collapse"} Sidebar</span>
              </Button>
            </div>
          </div>
          <div className="flex-1 overflow-auto">
            <nav className="flex-1 space-y-1 px-2 py-4">
              {currentMenuItems.map((item) => (
                <NavItem key={item.name} item={item} />
              ))}
            </nav>
          </div>
          <div className="border-t border-border p-2">
            <nav className="space-y-1">
              {bottomNavigation.map((item) => (
                <NavItem key={item.name} item={item} isBottom />
              ))}
            </nav>
          </div>
        </div>
      </>
    </TooltipProvider>
  )
}

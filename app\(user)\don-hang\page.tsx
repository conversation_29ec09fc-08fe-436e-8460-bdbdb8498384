"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Calendar, Clock, Video, AlertTriangle, CheckCircle, XCircle } from "lucide-react"
import { Order } from "@/api/models/order/view-order"
import { getUserOrders } from "@/api/orderAPI"
import { toast } from "sonner"
import { OrderStatusKey, OrderStatusConsts } from "@/lib/common/order-status-consts"

export default function OrdersPage() {
  const [activeTab, setActiveTab] = useState("tat-ca");

  const [orderList, setOrderList] = useState<Order[]>([]);

  const fetchData = async () => {
    try {
      const response = await getUserOrders();
      const listOrder = response.data.map((order) => {
        const createdAt = order.createdAt ? new Date(order.createdAt) : undefined
        return {
        ...order,
        status: OrderStatusConsts[order.status as OrderStatusKey]?.label || "Chờ xử lý",
        color: OrderStatusConsts[order.status as OrderStatusKey]?.color || "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200",
        icon: OrderStatusConsts[order.status as OrderStatusKey]?.icon || XCircle,
        createdAt: order.createdAt ? new Date(order.createdAt).toLocaleDateString() : "Chưa có",
        deliveryDate: createdAt
          ? new Date(createdAt.getTime() + 7 * 24 * 60 * 60 * 1000).toLocaleDateString("vi-VN")
          : "Chưa có",
        }
      });
      setOrderList(listOrder);
    } catch (error) {
      toast.error("Không thể tải đơn hàng. Vui lòng thử lại sau.");
    }
  }

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Đơn hàng của tôi</h1>
        <p className="text-muted-foreground">Quản lý và theo dõi tất cả đơn hàng của bạn</p>
      </div>

      <Tabs defaultValue="tat-ca" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="tat-ca">Tất cả</TabsTrigger>
          <TabsTrigger value="cho-xu-ly">Chờ xử lý</TabsTrigger>
          <TabsTrigger value="dang-xu-ly">Đang xử lý</TabsTrigger>
          <TabsTrigger value="da-gui">Đã gửi</TabsTrigger>
          <TabsTrigger value="hoan-thanh">Hoàn thành</TabsTrigger>
          <TabsTrigger value="khieu-nai">Khiếu nại</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {orderList.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <div className="rounded-full bg-primary/10 p-3">
                  <Video className="h-6 w-6 text-primary" />
                </div>
                <h3 className="mt-4 text-lg font-medium">Không có đơn hàng nào</h3>
                <p className="mt-2 text-center text-sm text-muted-foreground">
                  Bạn chưa có đơn hàng nào trong trạng thái này.
                </p>
                <Button className="mt-4" asChild>
                  <Link href="/trang-chu">Đặt video ngay</Link>
                </Button>
              </CardContent>
            </Card>
          ) : (
            orderList.map((order) => (
              <Card key={order.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex flex-col md:flex-row">
                    <div className="flex items-center gap-4 border-b md:border-b-0 md:border-r p-4 md:w-64">
                      <Avatar>
                        <AvatarImage src={order.talent.avatar || "/placeholder.svg"} alt={order.talent.name} />
                        <AvatarFallback>{order.talent.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-medium">{order.talent.name}</h3>
                        <p className="text-sm text-muted-foreground">{order.type}</p>
                      </div>
                    </div>
                    <div className="flex-1 p-4">
                      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                        <div>
                          <div className="flex items-center gap-2">
                            <Badge >
                              {order.status}
                            </Badge>
                            <span className="text-sm font-medium">#{order.id}</span>
                          </div>
                          <div className="mt-2 flex items-center gap-4">
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Calendar className="mr-1 h-4 w-4" />
                              Đặt: {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : "Chưa có"}
                            </div>
                            <div className="flex items-center text-sm text-muted-foreground">
                              <Clock className="mr-1 h-4 w-4" />
                              Hạn: {order.deliveryDate ? order.deliveryDate : "Chưa có"}
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-col md:items-end">
                          <div className="text-lg font-bold">
                            {new Intl.NumberFormat("vi-VN", {
                              style: "currency",
                              currency: "VND",
                            }).format(order.price || 0)}
                          </div>
                          <Button className="mt-2" asChild>
                            <Link href={`/don-hang/${order.id}`}>Chi tiết</Link>
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}

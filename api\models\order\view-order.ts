import { User } from "../user";

export interface Order {
  id?: number;
  type?: string;
  video_protocol_method?: string;
  talentId?: number;
  recipient?: string;
  status?: string;
  price?: number;
  paymentMethod?: string;
  paymentDate?: string | null;
  createdAt?: string;
  updatedAt?: string;
  request_details?: string;
  video_link?: string | null;
  user: User;
  talent: User;
  // support FE
  deliveryDate?: string; 
}
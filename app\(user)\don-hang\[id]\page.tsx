"use client"

import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Clock, Video, AlertTriangle, CheckCircle, XCircle, MessageSquare, Star } from "lucide-react"
import { toast } from "sonner"
import { cancelOrder, getOrderById, payOrder } from "@/api/orderAPI"
import { Order } from "@/api/models/order/view-order"
import { occasionList } from "@/components/user-order/occasion"
import { OrderStatusConsts, OrderStatus<PERSON>ey } from "@/lib/common/order-status-consts"

// Mock data for orders
const orders = [
  {
    id: "ord-001",
    talent: {
      id: "1",
      name: "Nguyễn Văn A",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent1",
    },
    service: "Video chúc mừng sinh nhật",
    price: 500000,
    status: "cho-xu-ly",
    createdAt: "2023-07-15",
    deliveryDate: "2023-07-18",
    recipient: "Lê Thị X",
    occasion: "Sinh nhật lần thứ 30",
    instructions: "Chúc mừng sinh nhật X, nhắc đến việc cô ấy thích du lịch và đọc sách.",
  },
  {
    id: "ord-002",
    talent: {
      id: "2",
      name: "Trần Thị B",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent2",
    },
    service: "Video động viên",
    price: 600000,
    status: "dang-xu-ly",
    createdAt: "2023-07-10",
    deliveryDate: "2023-07-14",
    recipient: "Phạm Văn Y",
    occasion: "Chuẩn bị cho kỳ thi quan trọng",
    instructions: "Động viên Y trước kỳ thi đại học, nhấn mạnh rằng cậu ấy đã chuẩn bị rất kỹ và sẽ làm tốt.",
  },
  {
    id: "ord-003",
    talent: {
      id: "3",
      name: "Lê Văn C",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent3",
    },
    service: "Video quảng cáo",
    price: 1500000,
    status: "da-gui",
    createdAt: "2023-07-05",
    deliveryDate: "2023-07-08",
    recipient: "Công ty Z",
    occasion: "Ra mắt sản phẩm mới",
    instructions: "Giới thiệu sản phẩm mới của công ty Z, nhấn mạnh các tính năng độc đáo và lợi ích.",
    videoUrl: "https://example.com/video.mp4",
  },
  {
    id: "ord-004",
    talent: {
      id: "4",
      name: "Phạm Thị D",
      avatar: "https://api.dicebear.com/7.x/avataaars/svg?seed=talent4",
    },
    service: "Video chúc mừng sinh nhật",
    price: 600000,
    status: "hoan-thanh",
    createdAt: "2023-06-25",
    deliveryDate: "2023-06-28",
    recipient: "Nguyễn Văn W",
    occasion: "Sinh nhật lần thứ 40",
    instructions: "Chúc mừng sinh nhật W, nhắc đến sở thích bóng đá và ẩm thực của anh ấy.",
    videoUrl: "https://example.com/video.mp4",
    review: {
      rating: 5,
      comment: "Video tuyệt vời, rất cá nhân hóa và chân thành. Cảm ơn nhiều!",
    },
  },
]

export default function OrderDetailPage() {
  const { id } = useParams()
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false)
  const [isComplaintDialogOpen, setIsComplaintDialogOpen] = useState(false)
  const [rating, setRating] = useState(5)
  const [reviewComment, setReviewComment] = useState("")
  const [complaintReason, setComplaintReason] = useState("");
  const [order, setOrder] = useState<Order | null>();

  // Find order by id
  const fetchData = async () => {
    try {
      const data = await getOrderById(parseInt(id as string));
      let orderData = data;
      const createdAt = orderData.createdAt ? new Date(orderData.createdAt) : undefined
      const deliveryDate = createdAt
        ? new Date(createdAt.getTime() + 7 * 24 * 60 * 60 * 1000)
        : undefined;
      let type = occasionList.find(o => o.name === orderData.type)?.displayName || orderData.type;
      setOrder({
        ...orderData,
        type: type,
        createdAt: createdAt?.toLocaleDateString("vi-VN"),
        deliveryDate: deliveryDate?.toLocaleDateString("vi-VN"),
      });

    } catch (error) {
      toast.error("Không thể tải thông tin đơn hàng.");
    }
  }

  useEffect(() => {
    fetchData();
  }, [id]);

  const handleAcceptVideo = () => {
    toast.success("Đã xác nhận nhận video. Đơn hàng hoàn thành!")
  }

  const handleSubmitReview = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    toast.success("Đánh giá đã được gửi thành công!")
    setIsReviewDialogOpen(false)
  }

  interface ComplaintEvent extends React.FormEvent<HTMLFormElement> {}

  const handleSubmitComplaint = (e: ComplaintEvent) => {
    e.preventDefault()
    toast.success("Khiếu nại đã được gửi. Chúng tôi sẽ xem xét và phản hồi sớm nhất.")
    setIsComplaintDialogOpen(false)
  }

  const handlePayment = async () => {
    try {
      const data = await payOrder(parseInt(id as string));
      toast.success("Thanh toán thành công!");
      fetchData();
    } catch (error) {
      toast.error("Thanh toán thất bại.");
    }
  }

  const handleCancelOrder = async () => {
    try {
      const data = await cancelOrder(parseInt(id as string));
      toast.success("Đơn hàng đã được huỷ thành công!");
      fetchData();
    } catch (error) {
      toast.error("Huỷ đơn hàng thất bại.");
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold">Chi tiết đơn hàng #{order?.id}</h1>
        <div className="flex items-center gap-2">
          <Badge>
            {OrderStatusConsts[order?.status as OrderStatusKey]?.label || "Chưa xác định"}
          </Badge>
        </div>
      </div>
      <div>
        {order?.status === "pending" && <Button onClick={handlePayment}>Thanh toán</Button>}
        {order?.status === "pending" && <Button className="ml-2" onClick={handleCancelOrder}>Huỷ đơn hàng</Button>}
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Thông tin đơn hàng</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium">Dịch vụ</h3>
                  <p className="text-muted-foreground">{order?.type}</p>
                </div>
                <div>
                  <h3 className="font-medium">Giá</h3>
                  <p className="text-lg font-bold">
                    {new Intl.NumberFormat("vi-VN", {
                      style: "currency",
                      currency: "VND",
                    }).format(order?.price || 0)}
                  </p>
                </div>
                <div>
                  <h3 className="font-medium">Ngày đặt</h3>
                  <p className="text-muted-foreground">{order?.createdAt}</p>
                </div>
                <div>
                  <h3 className="font-medium">Hạn giao</h3>
                  <p className="text-muted-foreground">{order?.deliveryDate}</p>
                </div>
              </div>
              <div>
                <h3 className="font-medium">Hướng dẫn chi tiết</h3>
                <p className="text-muted-foreground mt-1 whitespace-pre-line">{order?.request_details}</p>
              </div>
            </CardContent>
          </Card>

          {order?.video_link && (
            <Card>
              <CardHeader>
                <CardTitle>Video đã gửi</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="aspect-video bg-muted rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <Video className="h-12 w-12 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">Video preview</p>
                    <p className="text-xs text-muted-foreground">{order?.video_link}</p>
                  </div>
                </div>
                {order?.status === "da-gui" && (
                  <div className="flex gap-2 mt-4">
                    <Button onClick={handleAcceptVideo} className="flex-1">
                      Xác nhận nhận video
                    </Button>
                    <Dialog open={isComplaintDialogOpen} onOpenChange={setIsComplaintDialogOpen}>
                      <DialogTrigger asChild>
                        <Button variant="outline">Khiếu nại</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Gửi khiếu nại</DialogTitle>
                          <DialogDescription>Vui lòng mô tả vấn đề bạn gặp phải với video này.</DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleSubmitComplaint}>
                          <div className="space-y-4">
                            <Textarea
                              placeholder="Mô tả vấn đề..."
                              value={complaintReason}
                              onChange={(e) => setComplaintReason(e.target.value)}
                              required
                            />
                          </div>
                          <DialogFooter className="mt-4">
                            <Button type="button" variant="outline" onClick={() => setIsComplaintDialogOpen(false)}>
                              Hủy
                            </Button>
                            <Button type="submit">Gửi khiếu nại</Button>
                          </DialogFooter>
                        </form>
                      </DialogContent>
                    </Dialog>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* {order.review && (
            <Card>
              <CardHeader>
                <CardTitle>Đánh giá của bạn</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center mb-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-4 w-4 ${i < order.review.rating ? "text-yellow-500" : "text-gray-300"}`}
                      fill={i < order.review.rating ? "currentColor" : "none"}
                    />
                  ))}
                  <span className="ml-2 font-medium">{order.review.rating}/5</span>
                </div>
                <p className="text-muted-foreground">{order.review.comment}</p>
              </CardContent>
            </Card>
          )} */}

          {/* {order?.status === "hoan-thanh" && !order?.review && (
            <Card>
              <CardHeader>
                <CardTitle>Đánh giá đơn hàng</CardTitle>
                <CardDescription>Chia sẻ trải nghiệm của bạn để giúp người khác</CardDescription>
              </CardHeader>
              <CardContent>
                <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
                  <DialogTrigger asChild>
                    <Button>Viết đánh giá</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Đánh giá đơn hàng</DialogTitle>
                      <DialogDescription>Đánh giá chất lượng video và dịch vụ của talent</DialogDescription>
                    </DialogHeader>
                    <form onSubmit={handleSubmitReview}>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">Đánh giá</label>
                          <div className="flex items-center mt-1">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`h-6 w-6 cursor-pointer ${i < rating ? "text-yellow-500" : "text-gray-300"}`}
                                fill={i < rating ? "currentColor" : "none"}
                                onClick={() => setRating(i + 1)}
                              />
                            ))}
                          </div>
                        </div>
                        <Textarea
                          placeholder="Chia sẻ trải nghiệm của bạn..."
                          value={reviewComment}
                          onChange={(e) => setReviewComment(e.target.value)}
                          required
                        />
                      </div>
                      <DialogFooter className="mt-4">
                        <Button type="button" variant="outline" onClick={() => setIsReviewDialogOpen(false)}>
                          Hủy
                        </Button>
                        <Button type="submit">Gửi đánh giá</Button>
                      </DialogFooter>
                    </form>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>
          )} */}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Talent</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={order?.talent.avatar || "/placeholder.svg"} alt={order?.talent.name} />
                  <AvatarFallback>{order?.talent.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-medium">{order?.talent.name}</h3>
                  <p className="text-sm text-muted-foreground">Talent</p>
                </div>
              </div>
              <div className="mt-4 space-y-2">
                <Button className="w-full" variant="outline">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Nhắn tin
                </Button>
                <Button className="w-full" variant="outline">
                  Xem hồ sơ
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* <Card>
            <CardHeader>
              <CardTitle>Trạng thái đơn hàng</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-green-100 p-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium">Đặt hàng thành công</p>
                    <p className="text-sm text-muted-foreground">{order?.createdAt}</p>
                  </div>
                </div>

                {["dang-xu-ly", "da-gui", "hoan-thanh"].includes(order?.status ?? "") && (
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-blue-100 p-2">
                      <Video className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">Talent đã nhận đơn</p>
                      <p className="text-sm text-muted-foreground">Đang thực hiện video</p>
                    </div>
                  </div>
                )}

                {["da-gui", "hoan-thanh"].includes(order?.status ?? "") && (
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-purple-100 p-2">
                      <CheckCircle className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <p className="font-medium">Video đã gửi</p>
                      <p className="text-sm text-muted-foreground">Chờ xác nhận</p>
                    </div>
                  </div>
                )}

                {order?.status === "hoan-thanh" && (
                  <div className="flex items-center gap-3">
                    <div className="rounded-full bg-green-100 p-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </div>
                    <div>
                      <p className="font-medium">Hoàn thành</p>
                      <p className="text-sm text-muted-foreground">Đơn hàng đã hoàn tất</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card> */}
        </div>
      </div>
    </div>
  )
}
